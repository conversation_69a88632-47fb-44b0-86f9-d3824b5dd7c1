/**
 * Firebase Authentication middleware for Express backend
 * Provides secure token verification and user authentication
 */

import { Request, Response, NextFunction } from 'express';
import * as admin from 'firebase-admin';
import { logger } from '../utils/logger';
import { env } from '../config/env';

// Extend Express Request type to include user information
declare global {
  namespace Express {
    interface Request {
      currentUser?: admin.auth.DecodedIdToken;
      userId?: string;
      userEmail?: string;
    }
  }
}

class FirebaseAuth {
  private initialized: boolean = false;

  /**
   * Initialize Firebase Admin SDK
   */
  public initializeApp(): void {
    try {
      // Check if Firebase is already initialized
      if (admin.apps.length === 0) {
        this.initializeFirebase();
      }

      this.initialized = true;
      logger.info('Firebase Admin SDK initialized successfully');
    } catch (error) {
      logger.error(`Failed to initialize Firebase Admin SDK: ${error}`);
      this.initialized = false;
    }
  }

  /**
   * Initialize Firebase Admin SDK with credentials
   */
  private initializeFirebase(): void {
    // Try to get credentials from environment variables
    const firebaseConfig = this.getFirebaseConfigFromEnv();

    if (firebaseConfig) {
      // Initialize with service account credentials from environment
      const credential = admin.credential.cert(firebaseConfig);
      admin.initializeApp({
        credential
      });
      logger.info('Firebase initialized with environment credentials');
    } else if (env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH) {
      // Try to initialize with service account key file
      try {
        const credential = admin.credential.cert(env.FIREBASE_SERVICE_ACCOUNT_KEY_PATH);
        admin.initializeApp({
          credential
        });
        logger.info('Firebase initialized with service account key file');
      } catch (error) {
        // Fall back to default credentials if file doesn't exist or is invalid
        admin.initializeApp();
        logger.info('Firebase initialized with default credentials (fallback)');
      }
    } else {
      // Initialize with default credentials (for development)
      admin.initializeApp();
      logger.info('Firebase initialized with default credentials');
    }
  }

  /**
   * Get Firebase configuration from environment variables
   */
  private getFirebaseConfigFromEnv(): admin.credential.Credential | null {
    const requiredFields = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_PRIVATE_KEY_ID',
      'FIREBASE_PRIVATE_KEY',
      'FIREBASE_CLIENT_EMAIL',
      'FIREBASE_CLIENT_ID',
      'FIREBASE_AUTH_URI',
      'FIREBASE_TOKEN_URI'
    ];

    const config: Record<string, string> = {};
    
    for (const field of requiredFields) {
      const value = process.env[field];
      if (!value) {
        logger.warning(`Missing environment variable: ${field}`);
        return null;
      }
      
      // Convert environment variable name to the format expected by Firebase
      const key = field.toLowerCase().replace('firebase_', '');
      config[key] = value;
    }

    // Handle private key formatting
    if (config.private_key) {
      // Replace escaped newlines with actual newlines
      config.private_key = config.private_key.replace(/\\n/g, '\n');
    }

    // Add required type field for service account
    config.type = 'service_account';

    return admin.credential.cert(config as admin.ServiceAccount);
  }

  /**
   * Verify Firebase ID token and return decoded claims
   */
  public async verifyToken(idToken: string): Promise<admin.auth.DecodedIdToken | null> {
    if (!this.initialized) {
      logger.error('Firebase not initialized');
      return null;
    }

    try {
      // Verify the ID token with clock skew tolerance
      const decodedToken = await admin.auth().verifyIdToken(idToken, true);
      return decodedToken;
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('expired')) {
          logger.warning('Expired ID token');
        } else {
          logger.warning(`Invalid ID token: ${error.message}`);
        }
      } else {
        logger.error(`Token verification failed: ${error}`);
      }
      return null;
    }
  }

  /**
   * Get user information from Firebase Auth
   */
  public async getUserInfo(uid: string): Promise<Record<string, any> | null> {
    if (!this.initialized) {
      return null;
    }

    try {
      const userRecord = await admin.auth().getUser(uid);
      return {
        uid: userRecord.uid,
        email: userRecord.email,
        emailVerified: userRecord.emailVerified,
        displayName: userRecord.displayName,
        photoURL: userRecord.photoURL,
        disabled: userRecord.disabled,
        providerData: userRecord.providerData.map(provider => ({
          uid: provider.uid,
          email: provider.email,
          providerId: provider.providerId
        }))
      };
    } catch (error) {
      logger.error(`Failed to get user info: ${error}`);
      return null;
    }
  }
}

// Create global Firebase Auth instance
export const firebaseAuth = new FirebaseAuth();

/**
 * Middleware to require Firebase authentication for Express routes
 */
export const requireAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  // Get the Authorization header
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    res.status(401).json({
      error: 'Missing Authorization header',
      code: 'MISSING_AUTH_HEADER'
    });
    return;
  }

  // Extract the token from "Bearer <token>"
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0].toLowerCase() !== 'bearer') {
    res.status(401).json({
      error: 'Invalid Authorization header format. Expected: Bearer <token>',
      code: 'INVALID_AUTH_HEADER'
    });
    return;
  }

  const token = parts[1];

  // Verify the token
  const decodedToken = await firebaseAuth.verifyToken(token);

  if (!decodedToken) {
    res.status(401).json({
      error: 'Invalid or expired token',
      code: 'INVALID_TOKEN'
    });
    return;
  }

  // Store user info in request object for use in routes
  req.currentUser = decodedToken;
  req.userId = decodedToken.uid;
  req.userEmail = decodedToken.email;

  next();
};

/**
 * Get the current authenticated user from request
 */
export const getCurrentUser = (req: Request): admin.auth.DecodedIdToken | undefined => {
  return req.currentUser;
};

/**
 * Get the current authenticated user's ID
 */
export const getCurrentUserId = (req: Request): string | undefined => {
  return req.userId;
};

/**
 * Get the current authenticated user's email
 */
export const getCurrentUserEmail = (req: Request): string | undefined => {
  return req.userEmail;
};

