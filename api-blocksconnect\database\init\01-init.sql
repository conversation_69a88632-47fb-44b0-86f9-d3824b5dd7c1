-- BlocksConnect Database Schema
-- This script initializes the database with proper tables for multi-tenant Minecraft server management

-- Enable UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table to store user information from Firebase
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Minecraft servers table with user ownership
CREATE TABLE minecraft_servers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    port INTEGER NOT NULL,
    version VARCHAR(50) NOT NULL,
    server_type VARCHAR(50) DEFAULT 'vanilla',
    memory VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'stopped',
    container_id VARCHAR(255),
    backup_enabled BOOLEAN DEFAULT FALSE,
    server_config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_port_per_user UNIQUE (owner_id, port),
    CONSTRAINT valid_status CHECK (status IN ('stopped', 'starting', 'running', 'stopping', 'error')),
    CONSTRAINT valid_server_type CHECK (server_type IN ('vanilla', 'forge', 'fabric', 'paper', 'spigot', 'bukkit'))
);

-- Server backups table
CREATE TABLE server_backups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    server_id UUID NOT NULL REFERENCES minecraft_servers(id) ON DELETE CASCADE,
    backup_name VARCHAR(255) NOT NULL,
    backup_path VARCHAR(500) NOT NULL,
    backup_size BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_backup_name_per_server UNIQUE (server_id, backup_name)
);

-- Server logs table for audit trail
CREATE TABLE server_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    server_id UUID NOT NULL REFERENCES minecraft_servers(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_minecraft_servers_owner_id ON minecraft_servers(owner_id);
CREATE INDEX idx_minecraft_servers_status ON minecraft_servers(status);
CREATE INDEX idx_minecraft_servers_created_at ON minecraft_servers(created_at);
CREATE INDEX idx_server_backups_server_id ON server_backups(server_id);
CREATE INDEX idx_server_logs_server_id ON server_logs(server_id);
CREATE INDEX idx_server_logs_user_id ON server_logs(user_id);
CREATE INDEX idx_server_logs_created_at ON server_logs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_minecraft_servers_updated_at BEFORE UPDATE ON minecraft_servers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (optional - remove in production)
-- This will be useful for testing the migration
INSERT INTO users (firebase_uid, email, display_name) VALUES 
('test-user-1', '<EMAIL>', 'Test User')
ON CONFLICT (firebase_uid) DO NOTHING;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO blocksconnect;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO blocksconnect;
