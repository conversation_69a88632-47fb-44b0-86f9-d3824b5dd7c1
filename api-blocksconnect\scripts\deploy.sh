#!/bin/bash

# BlocksConnect Deployment Script
# This script helps deploy the new database-backed system

set -e  # Exit on any error

echo "🚀 BlocksConnect Deployment Script"
echo "=================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env file from template"
        echo "⚠️  Please edit .env file with your configuration before continuing"
        echo "   Required settings:"
        echo "   - POSTGRES_PASSWORD"
        echo "   - FIREBASE_* settings"
        echo "   - PREVIEW_ACCESS_EMAILS"
        exit 1
    else
        echo "❌ .env.example not found. Cannot create .env file."
        exit 1
    fi
fi

# Source environment variables
source .env

# Check required environment variables
echo "🔍 Checking environment configuration..."

required_vars=("POSTGRES_PASSWORD" "FIREBASE_PROJECT_ID" "PREVIEW_ACCESS_EMAILS")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Missing required environment variables:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo "   Please update your .env file"
    exit 1
fi

echo "✅ Environment configuration looks good"

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Build and start PostgreSQL first
echo "🐘 Starting PostgreSQL database..."
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if docker-compose exec -T postgres pg_isready -U ${POSTGRES_USER:-blocksconnect} > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    
    attempt=$((attempt + 1))
    echo "   Attempt $attempt/$max_attempts - waiting..."
    sleep 2
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ PostgreSQL failed to start within expected time"
    echo "   Check logs: docker-compose logs postgres"
    exit 1
fi

# Start all services
echo "🚀 Starting all services..."
docker-compose up -d

# Wait a moment for services to start
sleep 5

# Check service health
echo "🏥 Checking service health..."

services=("postgres" "gateway" "minecraft")
for service in "${services[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
        echo "   Check logs: docker-compose logs $service"
    fi
done

# Test database connection
echo "🔗 Testing database connection..."
if docker-compose exec -T postgres psql -U ${POSTGRES_USER:-blocksconnect} -d blocksconnect -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    echo "   Check logs: docker-compose logs postgres"
    exit 1
fi

# Check if migration is needed
echo "🔍 Checking for existing data to migrate..."
migration_needed=false

# Check for servers.json files
if [ -f "services/gateway/servers.json" ] || [ -f "services/minecraft/servers.json" ]; then
    if [ -s "services/gateway/servers.json" ] || [ -s "services/minecraft/servers.json" ]; then
        migration_needed=true
    fi
fi

if [ "$migration_needed" = true ]; then
    echo "📦 Found existing server data that needs migration"
    echo "   Run the migration script: python scripts/migrate_to_database.py"
    echo "   Then test the migration: python scripts/test_migration.py"
else
    echo "✅ No existing data found - fresh installation"
fi

# Display service URLs
echo ""
echo "🌐 Service URLs:"
echo "   Gateway API: http://localhost:5000"
echo "   Minecraft Service: http://localhost:5001"
echo "   PostgreSQL: localhost:5432"

# Display next steps
echo ""
echo "🎯 Next Steps:"
if [ "$migration_needed" = true ]; then
    echo "   1. Run migration: python scripts/migrate_to_database.py"
    echo "   2. Test migration: python scripts/test_migration.py"
    echo "   3. Access your application and verify functionality"
else
    echo "   1. Access your application and create your first server"
    echo "   2. Verify multi-user functionality works correctly"
fi

echo "   4. Monitor logs: docker-compose logs -f"
echo "   5. Check service status: docker-compose ps"

echo ""
echo "✅ Deployment completed successfully!"
echo "   Your BlocksConnect system is now running with PostgreSQL backend"
