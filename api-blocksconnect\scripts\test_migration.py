#!/usr/bin/env python3
"""
Test script to verify the database migration and multi-tenancy implementation
This script will test:
1. Database connectivity
2. User creation and management
3. Server operations with user isolation
4. Audit logging functionality
"""

import os
import sys
import json
import requests
from datetime import datetime

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

try:
    from database import UserManager, ServerManager, LogManager, db_manager
except ImportError as e:
    print(f"Error importing database modules: {e}")
    print("Make sure you have set up the database and installed dependencies.")
    sys.exit(1)

def test_database_connection():
    """Test basic database connectivity"""
    print("Testing database connection...")
    try:
        result = db_manager.execute_query("SELECT 1 as test", fetch_one=True)
        if result and result['test'] == 1:
            print("✓ Database connection successful")
            return True
        else:
            print("✗ Database connection failed - unexpected result")
            return False
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def test_user_management():
    """Test user creation and management"""
    print("\nTesting user management...")
    try:
        # Create test users
        user1 = UserManager.get_or_create_user(
            firebase_uid="test-user-1",
            email="<EMAIL>",
            display_name="Test User 1"
        )
        
        user2 = UserManager.get_or_create_user(
            firebase_uid="test-user-2", 
            email="<EMAIL>",
            display_name="Test User 2"
        )
        
        print(f"✓ Created/found user 1: {user1['email']} (ID: {user1['id']})")
        print(f"✓ Created/found user 2: {user2['email']} (ID: {user2['id']})")
        
        # Test user retrieval
        retrieved_user = UserManager.get_user_by_firebase_uid("test-user-1")
        if retrieved_user and retrieved_user['email'] == "<EMAIL>":
            print("✓ User retrieval successful")
        else:
            print("✗ User retrieval failed")
            return False
            
        return user1, user2
    except Exception as e:
        print(f"✗ User management test failed: {e}")
        return False

def test_server_isolation(user1, user2):
    """Test server creation and user isolation"""
    print("\nTesting server isolation...")
    try:
        # Create servers for each user
        server1 = ServerManager.create_server(
            owner_id=user1['id'],
            name="User1 Server",
            port=25565,
            version="1.20.1",
            memory="2G"
        )
        
        server2 = ServerManager.create_server(
            owner_id=user2['id'],
            name="User2 Server", 
            port=25565,  # Same port, different user
            version="1.19.4",
            memory="1G"
        )
        
        print(f"✓ Created server for user 1: {server1['name']} (Port: {server1['port']})")
        print(f"✓ Created server for user 2: {server2['name']} (Port: {server2['port']})")
        
        # Test user isolation - user1 should only see their server
        user1_servers = ServerManager.get_user_servers(user1['id'])
        user2_servers = ServerManager.get_user_servers(user2['id'])
        
        if len(user1_servers) >= 1 and user1_servers[0]['name'] == "User1 Server":
            print("✓ User 1 can see their own server")
        else:
            print("✗ User 1 cannot see their own server")
            return False
            
        if len(user2_servers) >= 1 and user2_servers[0]['name'] == "User2 Server":
            print("✓ User 2 can see their own server")
        else:
            print("✗ User 2 cannot see their own server")
            return False
            
        # Test cross-user access (should fail)
        user1_accessing_user2_server = ServerManager.get_server_by_id(str(server2['id']), user1['id'])
        if user1_accessing_user2_server is None:
            print("✓ User isolation working - User 1 cannot access User 2's server")
        else:
            print("✗ User isolation failed - User 1 can access User 2's server")
            return False
            
        return server1, server2
    except Exception as e:
        print(f"✗ Server isolation test failed: {e}")
        return False

def test_port_availability(user1, user2):
    """Test port availability checking"""
    print("\nTesting port availability...")
    try:
        # Test port availability for user 1 (should be false for 25565)
        port_available = ServerManager.check_port_availability(25565, user1['id'])
        if not port_available:
            print("✓ Port availability check working - port 25565 is in use by user 1")
        else:
            print("✗ Port availability check failed - port 25565 should be in use")
            return False
            
        # Test port availability for user 2 (should be false for 25565)
        port_available = ServerManager.check_port_availability(25565, user2['id'])
        if not port_available:
            print("✓ Port availability check working - port 25565 is in use by user 2")
        else:
            print("✗ Port availability check failed - port 25565 should be in use")
            return False
            
        # Test available port
        port_available = ServerManager.check_port_availability(25566, user1['id'])
        if port_available:
            print("✓ Port availability check working - port 25566 is available")
        else:
            print("✗ Port availability check failed - port 25566 should be available")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Port availability test failed: {e}")
        return False

def test_audit_logging(user1, server1):
    """Test audit logging functionality"""
    print("\nTesting audit logging...")
    try:
        # Log some test actions
        LogManager.log_server_action(
            server_id=str(server1['id']),
            user_id=user1['id'],
            action='test_action',
            details={'test': 'data', 'timestamp': datetime.now().isoformat()}
        )
        
        # Retrieve logs
        logs = db_manager.execute_query(
            "SELECT * FROM server_logs WHERE server_id = %s AND user_id = %s",
            (str(server1['id']), user1['id'])
        )
        
        if logs and len(logs) > 0:
            print(f"✓ Audit logging working - found {len(logs)} log entries")
            for log in logs:
                print(f"  - {log['action']} at {log['created_at']}")
        else:
            print("✗ Audit logging failed - no log entries found")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Audit logging test failed: {e}")
        return False

def test_server_operations(user1, server1):
    """Test server status updates"""
    print("\nTesting server operations...")
    try:
        # Test status update
        updated_count = ServerManager.update_server_status(
            str(server1['id']), 
            'running', 
            'test-container-id'
        )
        
        if updated_count > 0:
            print("✓ Server status update successful")
        else:
            print("✗ Server status update failed")
            return False
            
        # Verify the update
        updated_server = ServerManager.get_server_by_id(str(server1['id']), user1['id'])
        if updated_server and updated_server['status'] == 'running':
            print("✓ Server status verification successful")
        else:
            print("✗ Server status verification failed")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Server operations test failed: {e}")
        return False

def cleanup_test_data(user1, user2):
    """Clean up test data"""
    print("\nCleaning up test data...")
    try:
        # Delete test servers
        db_manager.execute_delete(
            "DELETE FROM minecraft_servers WHERE owner_id IN (%s, %s)",
            (user1['id'], user2['id'])
        )
        
        # Delete test users
        db_manager.execute_delete(
            "DELETE FROM users WHERE id IN (%s, %s)",
            (user1['id'], user2['id'])
        )
        
        print("✓ Test data cleaned up successfully")
        return True
    except Exception as e:
        print(f"✗ Cleanup failed: {e}")
        return False

def main():
    """Run all tests"""
    print("BlocksConnect Migration Test Suite")
    print("=" * 40)
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Database connection failed. Cannot proceed with tests.")
        sys.exit(1)
    
    # Test user management
    users = test_user_management()
    if not users:
        print("\n❌ User management tests failed.")
        sys.exit(1)
    
    user1, user2 = users
    
    # Test server isolation
    servers = test_server_isolation(user1, user2)
    if not servers:
        print("\n❌ Server isolation tests failed.")
        cleanup_test_data(user1, user2)
        sys.exit(1)
    
    server1, server2 = servers
    
    # Test port availability
    if not test_port_availability(user1, user2):
        print("\n❌ Port availability tests failed.")
        cleanup_test_data(user1, user2)
        sys.exit(1)
    
    # Test audit logging
    if not test_audit_logging(user1, server1):
        print("\n❌ Audit logging tests failed.")
        cleanup_test_data(user1, user2)
        sys.exit(1)
    
    # Test server operations
    if not test_server_operations(user1, server1):
        print("\n❌ Server operations tests failed.")
        cleanup_test_data(user1, user2)
        sys.exit(1)
    
    # Cleanup
    if not cleanup_test_data(user1, user2):
        print("\n⚠️  Cleanup failed - you may need to manually clean test data.")
    
    print("\n" + "=" * 40)
    print("🎉 All tests passed successfully!")
    print("✓ Database migration is working correctly")
    print("✓ Multi-tenancy is properly implemented")
    print("✓ User isolation is functioning")
    print("✓ Audit logging is operational")
    print("\nYour BlocksConnect migration is ready for production use!")

if __name__ == "__main__":
    main()
