{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_65fe3fd6._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_12a3b8ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "o0ZYA6Addiyumlp5vP3Ybme8Wp/5AW8QLzPtj8SlCuE=", "__NEXT_PREVIEW_MODE_ID": "7f812625ffbccf2122cef555b9931800", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5e442cfe4d8b026743441e17381ffd5a760d6649cb6f97d4526ae6bed8ac0d19", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5a2520da98621215443fb478b268b080502a80a393768a4a2f0621386b27fb9c"}}}, "sortedMiddleware": ["/"], "functions": {}}