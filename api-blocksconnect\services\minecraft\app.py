"""
Minecraft Service - Dedicated Minecraft Server Management
This service handles all Minecraft server operations with multi-tenant support.
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import os
import json
import uuid
import docker
import sys
from datetime import datetime
import shutil
from asgiref.wsgi import WsgiToAsgi

# Import database utilities
try:
    from database import UserManager, ServerManager, LogManager, db_manager
    print("Successfully imported database utilities", file=sys.stderr)
except ImportError as e:
    print(f"Failed to import database utilities: {e}", file=sys.stderr)
    print("Database features will not be available", file=sys.stderr)

    # Create a fallback implementation that uses the old JSON system
    def get_servers_fallback():
        """Fallback to JSON-based server storage"""
        try:
            servers_file = os.path.join(os.path.dirname(__file__), 'servers.json')
            if os.path.exists(servers_file):
                with open(servers_file, 'r') as f:
                    return json.load(f)
        except:
            pass
        return []

    def save_servers_fallback(servers):
        """Fallback to JSON-based server storage"""
        try:
            servers_file = os.path.join(os.path.dirname(__file__), 'servers.json')
            with open(servers_file, 'w') as f:
                json.dump(servers, f, indent=2)
        except Exception as e:
            print(f"Error saving servers: {e}", file=sys.stderr)

    # Override the get_servers function to use fallback
    def get_servers_override():
        return get_servers_fallback()

    # Mock classes for database functionality
    class MockUserManager:
        @staticmethod
        def get_or_create_user(*args, **kwargs):
            return {'id': 'fallback-user', 'email': '<EMAIL>'}

    class MockServerManager:
        @staticmethod
        def get_user_servers(*args, **kwargs):
            return get_servers_fallback()
        @staticmethod
        def get_server_by_id(server_id, user_id=None):
            servers = get_servers_fallback()
            return next((s for s in servers if s['id'] == server_id), None)
        @staticmethod
        def create_server(*args, **kwargs):
            return {'id': str(uuid.uuid4()), 'name': 'Fallback Server'}
        @staticmethod
        def update_server_status(*args, **kwargs):
            return 1
        @staticmethod
        def delete_server(*args, **kwargs):
            return 1
        @staticmethod
        def check_port_availability(port, user_id, exclude_server_id=None):
            servers = get_servers_fallback()
            for server in servers:
                if server.get('port') == port and server.get('id') != exclude_server_id:
                    return False
            return True

    class MockLogManager:
        @staticmethod
        def log_server_action(*args, **kwargs):
            pass

    class MockDBManager:
        def execute_update(self, *args, **kwargs):
            return 1

    UserManager = MockUserManager()
    ServerManager = MockServerManager()
    LogManager = MockLogManager()
    db_manager = MockDBManager()

    # Override the global get_servers function
    globals()['get_servers'] = get_servers_override

def get_user_context():
    """Get user context from gateway headers"""
    user_id = request.headers.get('X-User-ID')
    user_email = request.headers.get('X-User-Email')
    user_context_str = request.headers.get('X-User-Context')

    user_context = {}
    if user_context_str:
        try:
            user_context = json.loads(user_context_str)
        except:
            pass

    return {
        'user_id': user_id,
        'user_email': user_email,
        'context': user_context
    }

def require_user():
    """Decorator to ensure user context is available"""
    def decorator(f):
        def wrapper(*args, **kwargs):
            user_context = get_user_context()
            if not user_context.get('user_id'):
                return jsonify({'error': 'User authentication required'}), 401
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

def get_current_user():
    """Get current user from database, create if doesn't exist"""
    user_context = get_user_context()
    if not user_context.get('user_id'):
        return None

    # Get or create user in database
    user = UserManager.get_or_create_user(
        firebase_uid=user_context['user_id'],
        email=user_context.get('user_email', ''),
        display_name=user_context.get('context', {}).get('display_name')
    )
    return user

app = Flask(__name__)

# Configure CORS
CORS(app, resources={
    r"/api/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"]
    }
})

# Initialize Docker client
docker_available = True
try:
    docker_client = docker.from_env()
    print("Docker client initialized successfully", file=sys.stderr)
except Exception as e:
    docker_available = False
    print(f"Warning: Docker is not available. Error: {e}", file=sys.stderr)

# Configuration
DATA_DIR = os.getenv('DATA_DIR', '/data')
BACKUPS_DIR = os.getenv('BACKUPS_DIR', '/backups')

# Ensure directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(BACKUPS_DIR, exist_ok=True)

# Legacy functions for backward compatibility (now using database)
def get_servers():
    """Get servers for current user from database"""
    user = get_current_user()
    if not user:
        return []

    try:
        servers = ServerManager.get_user_servers(user['id'])
        # Convert database format to API format
        return [convert_db_server_to_api(server) for server in servers]
    except Exception as e:
        print(f"Error fetching servers: {e}", file=sys.stderr)
        return []

def convert_db_server_to_api(db_server):
    """Convert database server format to API format"""
    return {
        'id': str(db_server['id']),
        'name': db_server['name'],
        'port': db_server['port'],
        'version': db_server['version'],
        'type': db_server['server_type'],
        'memory': db_server['memory'],
        'status': db_server['status'],
        'container_id': db_server.get('container_id'),
        'backup_enabled': db_server.get('backup_enabled', False),
        'created_at': db_server['created_at'].isoformat() if db_server.get('created_at') else None
    }

def save_servers(servers):
    """Legacy function - no longer needed with database"""
    pass

def create_server_id():
    """Create a unique server ID"""
    return str(uuid.uuid4())

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'minecraft-service',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'docker_available': docker_available
    })

# List all Minecraft servers
@app.route('/api/servers', methods=['GET'])
@require_user()
def list_servers():
    """List all Minecraft servers for the current user"""
    return jsonify(get_servers())

# Get server details
@app.route('/api/servers/<server_id>', methods=['GET'])
@require_user()
def get_server_details(server_id):
    """Get detailed information about a specific Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Convert to API format
        api_server = convert_db_server_to_api(server)

        # Add real-time status if Docker is available
        if docker_available and server.get('container_id'):
            try:
                container = docker_client.containers.get(server['container_id'])
                api_server['runtime_status'] = container.status
                api_server['runtime_info'] = {
                    'created': container.attrs['Created'],
                    'started': container.attrs['State'].get('StartedAt'),
                    'ports': container.attrs['NetworkSettings']['Ports']
                }
            except Exception as e:
                print(f"Error getting container info: {e}", file=sys.stderr)
                api_server['runtime_status'] = 'unknown'

        return jsonify(api_server)
    except Exception as e:
        print(f"Error fetching server details: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to fetch server details'}), 500

# Create new server
@app.route('/api/servers', methods=['POST'])
@require_user()
def create_server():
    """Create a new Minecraft server for the current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    data = request.json

    # Validate required fields
    required_fields = ['name', 'port', 'version', 'memory']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400

    # Check if port is available for this user
    if not ServerManager.check_port_availability(data['port'], user['id']):
        return jsonify({'error': f'Port {data["port"]} is already in use by another server'}), 400

    try:
        # Create server in database
        server = ServerManager.create_server(
            owner_id=user['id'],
            name=data['name'],
            port=data['port'],
            version=data['version'],
            server_type=data.get('type', 'vanilla'),
            memory=data['memory'],
            server_config=data.get('config', {})
        )

        # Create server directory
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        os.makedirs(server_dir, exist_ok=True)

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_created',
            details={'name': data['name'], 'port': data['port'], 'version': data['version']}
        )

        return jsonify(convert_db_server_to_api(server)), 201
    except Exception as e:
        print(f"Error creating server: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to create server'}), 500

# Start server
@app.route('/api/servers/<server_id>/start', methods=['POST'])
@require_user()
def start_server(server_id):
    """Start a Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    if not docker_available:
        return jsonify({'error': 'Docker is not available'}), 500

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Create container name
        container_name = f"minecraft-server-{str(server['id'])[:8]}"
        server_dir = os.path.join(DATA_DIR, str(server['id']))

        # Start the container on a dedicated network for Minecraft servers
        container = docker_client.containers.run(
            'itzg/minecraft-server:latest',
            detach=True,
            name=container_name,
            ports={25565: server['port']},
            environment={
                'EULA': 'TRUE',
                'VERSION': server['version'],
                'MEMORY': server['memory'],
                'TYPE': server.get('server_type', 'vanilla').upper()
            },
            volumes={
                server_dir: {
                    'bind': '/data',
                    'mode': 'rw'
                }
            },
            network='minecraft-servers-network',
            restart_policy={"Name": "unless-stopped"}
        )

        # Update server status in database
        ServerManager.update_server_status(str(server['id']), 'running', container.id)

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_started',
            details={'container_id': container.id}
        )

        return jsonify({
            'status': 'running',
            'message': f'Server {server["name"]} started successfully',
            'container_id': container.id
        })

    except Exception as e:
        print(f"Error starting server: {e}", file=sys.stderr)
        return jsonify({'error': str(e)}), 500

# Stop server
@app.route('/api/servers/<server_id>/stop', methods=['POST'])
@require_user()
def stop_server(server_id):
    """Stop a Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        if not server.get('container_id'):
            return jsonify({'error': 'Server is not running'}), 400

        if docker_available:
            container = docker_client.containers.get(server['container_id'])
            container.stop(timeout=30)
            container.remove()

        # Update server status in database
        ServerManager.update_server_status(str(server['id']), 'stopped')

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_stopped',
            details={}
        )

        return jsonify({
            'status': 'stopped',
            'message': f'Server {server["name"]} stopped successfully'
        })

    except Exception as e:
        print(f"Error stopping server: {e}", file=sys.stderr)
        return jsonify({'error': str(e)}), 500

# Delete server
@app.route('/api/servers/<server_id>', methods=['DELETE'])
@require_user()
def delete_server(server_id):
    """Delete a Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        server_name = server['name']

        # Stop server if running
        if server.get('container_id') and docker_available:
            try:
                container = docker_client.containers.get(server['container_id'])
                container.stop(timeout=30)
                container.remove()
            except Exception as e:
                print(f"Error stopping container: {e}", file=sys.stderr)

        # Remove server directory
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        if os.path.exists(server_dir):
            shutil.rmtree(server_dir)

        # Delete from database
        deleted_count = ServerManager.delete_server(str(server['id']), user['id'])
        if deleted_count == 0:
            return jsonify({'error': 'Failed to delete server from database'}), 500

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_deleted',
            details={'server_name': server_name}
        )

        return jsonify({'message': f'Server {server_name} deleted successfully'})

    except Exception as e:
        print(f"Error deleting server: {e}", file=sys.stderr)
        return jsonify({'error': str(e)}), 500

# Get server logs
@app.route('/api/servers/<server_id>/logs', methods=['GET'])
@require_user()
def get_server_logs(server_id):
    """Get server logs for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        if docker_available and server.get('container_id'):
            container = docker_client.containers.get(server['container_id'])
            logs = container.logs(tail=100).decode('utf-8')
            return jsonify({'logs': logs.split('\n')})
        else:
            return jsonify({'logs': ['Server not running or Docker unavailable']})
    except Exception as e:
        return jsonify({'error': f'Failed to get logs: {str(e)}'}), 500

# Send command to server
@app.route('/api/servers/<server_id>/command', methods=['POST'])
@require_user()
def send_server_command(server_id):
    """Send command to user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.json
        command = data.get('command')
        if not command:
            return jsonify({'error': 'Command is required'}), 400

        if docker_available and server.get('container_id'):
            container = docker_client.containers.get(server['container_id'])
            # Execute command in the container
            result = container.exec_run(f'rcon-cli {command}')

            # Log the command
            LogManager.log_server_action(
                server_id=str(server['id']),
                user_id=user['id'],
                action='command_sent',
                details={'command': command}
            )

            return jsonify({
                'response': result.output.decode('utf-8') if result.output else 'Command executed',
                'exit_code': result.exit_code
            })
        else:
            return jsonify({'error': 'Server not running or Docker unavailable'}), 400
    except Exception as e:
        return jsonify({'error': f'Failed to send command: {str(e)}'}), 500

# Toggle server backup
@app.route('/api/servers/<server_id>/backup', methods=['POST'])
@require_user()
def toggle_server_backup(server_id):
    """Toggle server backup for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Toggle backup status in database
        new_backup_status = not server.get('backup_enabled', False)

        # Update server config
        server_config = server.get('server_config', {})
        server_config['backup_enabled'] = new_backup_status

        # Update in database (this would need a new method in ServerManager)
        db_manager.execute_update(
            "UPDATE minecraft_servers SET server_config = %s WHERE id = %s AND owner_id = %s",
            (json.dumps(server_config), str(server['id']), user['id'])
        )

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_toggled',
            details={'backup_enabled': new_backup_status}
        )

        return jsonify({
            'backup_enabled': new_backup_status,
            'message': f'Backup {"enabled" if new_backup_status else "disabled"} for server {server["name"]}'
        })
    except Exception as e:
        return jsonify({'error': f'Failed to toggle backup: {str(e)}'}), 500

# Download server backup
@app.route('/api/servers/<server_id>/backup', methods=['GET'])
@require_user()
def download_server_backup(server_id):
    """Download server backup for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Create backup if it doesn't exist
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        backup_file = os.path.join(BACKUPS_DIR, f'{str(server["id"])}_backup.tar.gz')

        if not os.path.exists(backup_file):
            # Create backup
            import tarfile
            with tarfile.open(backup_file, 'w:gz') as tar:
                tar.add(server_dir, arcname=server['name'])

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_downloaded',
            details={'backup_file': backup_file}
        )

        from flask import send_file
        return send_file(backup_file, as_attachment=True,
                        download_name=f'{server["name"]}_backup.tar.gz')
    except Exception as e:
        return jsonify({'error': f'Failed to create/download backup: {str(e)}'}), 500

# Create ASGI application for uvicorn
asgi_app = WsgiToAsgi(app)

# Make the app directly importable for ASGI servers
#app = asgi_app  # This ensures compatibility with both WSGI and ASGI servers

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5001))
    host = os.getenv('HOST', '0.0.0.0')
    debug = os.getenv('DEBUG', 'False').lower() == 'true'

    print(f"Starting Minecraft Service on {host}:{port}", file=sys.stderr)
    app.run(host=host, port=port, debug=debug)