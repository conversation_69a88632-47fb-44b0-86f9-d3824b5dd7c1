#!/usr/bin/env python3
"""
Migration script to move from JSON-based server storage to PostgreSQL database
This script will:
1. Read existing servers.json files
2. Create users in the database
3. Migrate servers to the database with proper ownership
4. Backup the old JSON files
"""

import os
import json
import sys
import shutil
from datetime import datetime
from pathlib import Path

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

try:
    from database import UserManager, ServerManager, LogManager, db_manager
except ImportError as e:
    print(f"Error importing database modules: {e}")
    print("Make sure you have set up the database and installed dependencies.")
    sys.exit(1)

def find_servers_json_files():
    """Find all servers.json files in the project"""
    servers_files = []
    project_root = Path(__file__).parent.parent
    
    # Common locations for servers.json
    possible_locations = [
        project_root / "services" / "gateway" / "servers.json",
        project_root / "services" / "minecraft" / "servers.json",
        project_root / "data" / "servers.json",
        project_root / "servers.json"
    ]
    
    for location in possible_locations:
        if location.exists():
            servers_files.append(location)
    
    return servers_files

def load_servers_from_json(file_path):
    """Load servers from a JSON file"""
    try:
        with open(file_path, 'r') as f:
            servers = json.load(f)
        return servers if isinstance(servers, list) else []
    except (json.JSONDecodeError, FileNotFoundError) as e:
        print(f"Error reading {file_path}: {e}")
        return []

def create_default_user():
    """Create a default user for migrated servers"""
    # Try to create a default user for migration
    default_email = "<EMAIL>"
    default_uid = "migration-user-001"
    
    user = UserManager.get_or_create_user(
        firebase_uid=default_uid,
        email=default_email,
        display_name="Migration User"
    )
    
    print(f"Created/found default user: {user['email']} (ID: {user['id']})")
    return user

def migrate_server(server_data, owner_id):
    """Migrate a single server to the database"""
    try:
        # Map old format to new format
        server_config = {
            'name': server_data.get('name', 'Unnamed Server'),
            'port': server_data.get('port', 25565),
            'version': server_data.get('version', '1.20.1'),
            'server_type': server_data.get('type', 'vanilla'),
            'memory': server_data.get('memory', '1G'),
            'server_config': {
                'backup_enabled': server_data.get('backup_enabled', False),
                'backup': server_data.get('backup', False),
                'original_id': server_data.get('id'),  # Keep original ID for reference
                'migrated_at': datetime.now().isoformat()
            }
        }
        
        # Check if port is available for this user
        if not ServerManager.check_port_availability(server_config['port'], owner_id):
            print(f"  Warning: Port {server_config['port']} already in use, skipping server '{server_config['name']}'")
            return None
        
        # Create server in database
        new_server = ServerManager.create_server(
            owner_id=owner_id,
            **server_config
        )
        
        # Update status if the server was running
        if server_data.get('status') == 'running' and server_data.get('container_id'):
            ServerManager.update_server_status(
                str(new_server['id']), 
                'running', 
                server_data['container_id']
            )
        
        print(f"  Migrated server: {server_config['name']} (Port: {server_config['port']})")
        return new_server
        
    except Exception as e:
        print(f"  Error migrating server {server_data.get('name', 'Unknown')}: {e}")
        return None

def backup_json_file(file_path):
    """Create a backup of the JSON file"""
    backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"Backed up {file_path} to {backup_path}")
    return backup_path

def main():
    """Main migration function"""
    print("BlocksConnect Database Migration Tool")
    print("=" * 40)
    
    # Check database connection
    try:
        # Test database connection
        result = db_manager.execute_query("SELECT 1", fetch_one=True)
        if not result:
            raise Exception("Database connection test failed")
        print("✓ Database connection successful")
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        print("Please ensure PostgreSQL is running and DATABASE_URL is set correctly.")
        sys.exit(1)
    
    # Find servers.json files
    servers_files = find_servers_json_files()
    if not servers_files:
        print("No servers.json files found. Migration not needed.")
        return
    
    print(f"Found {len(servers_files)} servers.json file(s):")
    for file_path in servers_files:
        print(f"  - {file_path}")
    
    # Confirm migration
    response = input("\nProceed with migration? (y/N): ").strip().lower()
    if response != 'y':
        print("Migration cancelled.")
        return
    
    # Create default user for migration
    default_user = create_default_user()
    
    total_migrated = 0
    total_errors = 0
    
    # Process each servers.json file
    for file_path in servers_files:
        print(f"\nProcessing {file_path}...")
        
        # Load servers from JSON
        servers = load_servers_from_json(file_path)
        if not servers:
            print("  No servers found in file.")
            continue
        
        print(f"  Found {len(servers)} server(s) to migrate")
        
        # Backup the file
        backup_json_file(file_path)
        
        # Migrate each server
        for server_data in servers:
            migrated_server = migrate_server(server_data, default_user['id'])
            if migrated_server:
                total_migrated += 1
                
                # Log the migration
                LogManager.log_server_action(
                    server_id=str(migrated_server['id']),
                    user_id=default_user['id'],
                    action='server_migrated',
                    details={
                        'original_file': str(file_path),
                        'original_id': server_data.get('id'),
                        'migration_date': datetime.now().isoformat()
                    }
                )
            else:
                total_errors += 1
    
    print(f"\nMigration Summary:")
    print(f"  Servers migrated: {total_migrated}")
    print(f"  Errors: {total_errors}")
    
    if total_migrated > 0:
        print(f"\n✓ Migration completed successfully!")
        print(f"All migrated servers are owned by: {default_user['email']}")
        print(f"You can now start using the new database-backed system.")
        
        # Ask if user wants to remove old JSON files
        response = input("\nRemove old servers.json files? (y/N): ").strip().lower()
        if response == 'y':
            for file_path in servers_files:
                try:
                    os.remove(file_path)
                    print(f"Removed {file_path}")
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")
    else:
        print("No servers were migrated.")

if __name__ == "__main__":
    main()
