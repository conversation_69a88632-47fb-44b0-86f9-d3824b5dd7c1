@echo off
REM BlocksConnect Deployment Script for Windows
REM This script helps deploy the new database-backed system

echo 🚀 BlocksConnect Deployment Script
echo ==================================

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found. Creating from template...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ Created .env file from template
        echo ⚠️  Please edit .env file with your configuration before continuing
        echo    Required settings:
        echo    - POSTGRES_PASSWORD
        echo    - FIREBASE_* settings
        echo    - PREVIEW_ACCESS_EMAILS
        pause
        exit /b 1
    ) else (
        echo ❌ .env.example not found. Cannot create .env file.
        pause
        exit /b 1
    )
)

echo ✅ Environment configuration file found

REM Stop existing containers
echo 🛑 Stopping existing containers...
docker-compose down

REM Build and start PostgreSQL first
echo 🐘 Starting PostgreSQL database...
docker-compose up -d postgres

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 10 /nobreak >nul

REM Start all services
echo 🚀 Starting all services...
docker-compose up -d

REM Wait a moment for services to start
timeout /t 5 /nobreak >nul

REM Check service health
echo 🏥 Checking service health...
docker-compose ps

REM Display service URLs
echo.
echo 🌐 Service URLs:
echo    Gateway API: http://localhost:5000
echo    Minecraft Service: http://localhost:5001
echo    PostgreSQL: localhost:5432

REM Display next steps
echo.
echo 🎯 Next Steps:
echo    1. Check if migration is needed: dir services\gateway\servers.json services\minecraft\servers.json
echo    2. If servers.json files exist, run: python scripts/migrate_to_database.py
echo    3. Test migration: python scripts/test_migration.py
echo    4. Access your application and verify functionality
echo    5. Monitor logs: docker-compose logs -f
echo    6. Check service status: docker-compose ps

echo.
echo ✅ Deployment completed!
echo    Your BlocksConnect system is now running with PostgreSQL backend

pause
