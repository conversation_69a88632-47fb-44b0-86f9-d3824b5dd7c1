# BlocksConnect Environment Configuration
# Copy this file to .env and update the values

# Database Configuration
POSTGRES_USER=blocksconnect
POSTGRES_PASSWORD=your_secure_password_here
DATABASE_URL=******************************************************************/blocksconnect

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs

# Preview Access Control
PREVIEW_ACCESS_EMAILS=<EMAIL>,<EMAIL>
NEXT_PUBLIC_PREVIEW_ACCESS_EMAILS=<EMAIL>,<EMAIL>

# Service URLs
NEXT_PUBLIC_API_URL=http://localhost:5000/api
MINECRAFT_SERVICE_URL=http://minecraft:5001
FILE_HOSTING_SERVICE_URL=http://files:5002
DATABASE_SERVICE_URL=http://database:5003

# Environment Settings
NODE_ENV=development
FLASK_ENV=development
FLASK_DEBUG=true
FLASK_PORT=5000

# Security
JWT_SECRET=your-jwt-secret-key-here
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# Domain Configuration
MAIN_DOMAIN=localhost:3000
PANEL_DOMAIN=localhost:3000
NEXT_PUBLIC_MAIN_DOMAIN=localhost:3000
