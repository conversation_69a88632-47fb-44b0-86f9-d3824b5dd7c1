# BlocksConnect Database Migration Guide

This guide will help you migrate from the JSON-based server storage to the new PostgreSQL database system with multi-tenancy support.

## Overview

The migration includes:
- **Database Migration**: JSON files → PostgreSQL database
- **Multi-tenancy**: User isolation for server management
- **Enhanced Security**: Proper authentication and authorization
- **Better Performance**: Database queries instead of file operations
- **Audit Logging**: Track all server operations

## Prerequisites

1. **Docker and Docker Compose** installed
2. **Existing BlocksConnect installation** with servers.json files
3. **Firebase Authentication** configured

## Migration Steps

### Step 1: Backup Your Data

Before starting, backup your existing data:

```bash
# Backup servers.json files
cp api-blocksconnect/services/gateway/servers.json servers_backup.json
cp api-blocksconnect/services/minecraft/servers.json minecraft_servers_backup.json

# Backup server data directories
tar -czf server_data_backup.tar.gz api-blocksconnect/services/minecraft/servers/
```

### Step 2: Configure Environment

1. Copy the environment template:
```bash
cp api-blocksconnect/.env.example api-blocksconnect/.env
```

2. Update the `.env` file with your configuration:
```bash
# Database Configuration
POSTGRES_USER=blocksconnect
POSTGRES_PASSWORD=your_secure_password_here

# Firebase Configuration (use your existing values)
FIREBASE_PROJECT_ID=your-firebase-project-id
# ... other Firebase settings

# Preview Access (add authorized user emails)
PREVIEW_ACCESS_EMAILS=<EMAIL>,<EMAIL>
```

### Step 3: Start the New System

1. Stop the current system:
```bash
cd api-blocksconnect
docker-compose down
```

2. Start the new system with PostgreSQL:
```bash
docker-compose up -d postgres
# Wait for PostgreSQL to be ready
docker-compose up -d
```

3. Verify the database is running:
```bash
docker-compose logs postgres
```

### Step 4: Run the Migration Script

```bash
cd api-blocksconnect
python scripts/migrate_to_database.py
```

The migration script will:
- Find all existing servers.json files
- Create a default user for migrated servers
- Migrate servers to the database
- Backup original JSON files
- Provide a summary of the migration

### Step 5: Verify the Migration

1. Check the web interface:
   - Log in with an authorized email
   - Verify your servers are visible
   - Test server operations (start/stop)

2. Check the database:
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U blocksconnect -d blocksconnect

# Check migrated data
SELECT * FROM users;
SELECT * FROM minecraft_servers;
SELECT * FROM server_logs;
```

## New Features

### Multi-Tenancy

- **User Isolation**: Each user can only see and manage their own servers
- **Port Sharing**: Multiple users can use the same port numbers (isolated per user)
- **Secure Access**: All operations require proper authentication

### Enhanced API

All API endpoints now include user authentication:
- `GET /api/minecraft/servers` - List user's servers
- `POST /api/minecraft/servers` - Create server for current user
- `GET /api/minecraft/servers/{id}` - Get user's server details
- `POST /api/minecraft/servers/{id}/start` - Start user's server
- `POST /api/minecraft/servers/{id}/stop` - Stop user's server
- `DELETE /api/minecraft/servers/{id}` - Delete user's server

### Database Schema

```sql
-- Users table
users (id, firebase_uid, email, display_name, created_at, updated_at)

-- Servers table with user ownership
minecraft_servers (id, owner_id, name, port, version, server_type, memory, status, container_id, backup_enabled, server_config, created_at, updated_at)

-- Audit logging
server_logs (id, server_id, user_id, action, details, created_at)

-- Backup management
server_backups (id, server_id, backup_name, backup_path, backup_size, created_at)
```

## User Management

### Adding New Users

Users are automatically created when they first log in with Firebase Authentication. Ensure their email is in the `PREVIEW_ACCESS_EMAILS` environment variable.

### Transferring Server Ownership

To transfer servers between users:

```sql
-- Connect to database
docker-compose exec postgres psql -U blocksconnect -d blocksconnect

-- Find user IDs
SELECT id, email FROM users;

-- Transfer servers from one user to another
UPDATE minecraft_servers 
SET owner_id = 'new-user-id' 
WHERE owner_id = 'old-user-id' AND name = 'server-name';
```

## Troubleshooting

### Migration Issues

1. **Database Connection Failed**
   - Ensure PostgreSQL is running: `docker-compose ps postgres`
   - Check DATABASE_URL in .env file
   - Verify network connectivity

2. **No Servers Found**
   - Check if servers.json files exist
   - Verify file permissions
   - Look in common locations: services/gateway/, services/minecraft/

3. **Port Conflicts**
   - The migration script will skip servers with conflicting ports
   - Manually resolve conflicts by updating port numbers

### Runtime Issues

1. **Authentication Errors**
   - Verify Firebase configuration
   - Check user email is in PREVIEW_ACCESS_EMAILS
   - Ensure Firebase tokens are valid

2. **Server Not Found Errors**
   - User can only access their own servers
   - Check server ownership in database
   - Verify user authentication

### Performance Issues

1. **Slow Database Queries**
   - Check database indexes are created
   - Monitor connection pool usage
   - Consider increasing connection pool size

## Rollback Procedure

If you need to rollback to the JSON-based system:

1. Stop the new system:
```bash
docker-compose down
```

2. Restore backup files:
```bash
cp servers_backup.json api-blocksconnect/services/gateway/servers.json
```

3. Use the old docker-compose configuration (without PostgreSQL)

4. Start the old system

## Support

For issues or questions:
1. Check the logs: `docker-compose logs`
2. Review this migration guide
3. Check the database state
4. Verify environment configuration

## Next Steps

After successful migration:
1. **Remove old JSON files** (if migration script didn't do it)
2. **Update monitoring** to include database health checks
3. **Set up database backups** for production
4. **Configure user access** by updating PREVIEW_ACCESS_EMAILS
5. **Test all functionality** with multiple users
