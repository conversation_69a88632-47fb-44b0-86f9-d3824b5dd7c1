services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: blocksconnect
      POSTGRES_USER: ${POSTGRES_USER:-blocksconnect}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-blocksconnect_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - blocksconnect-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-blocksconnect}"]
      interval: 30s
      timeout: 10s
      retries: 3

  gateway:
    build:
      context: ./services/gateway
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./shared/config:/app/config
      - ./services/gateway:/app
    environment:
      - PYTHONPATH=/app
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - FIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID}
      - FIREBASE_AUTH_URI=${FIREBASE_AUTH_URI}
      - FIREBASE_TOKEN_URI=${FIREBASE_TOKEN_URI}
      - FIREBASE_AUTH_PROVIDER_X509_CERT_URL=${FIREBASE_AUTH_PROVIDER_X509_CERT_URL}
      - FIREBASE_CLIENT_X509_CERT_URL=${FIREBASE_CLIENT_X509_CERT_URL}
      - PREVIEW_ACCESS_EMAILS=${PREVIEW_ACCESS_EMAILS}
      - NEXT_PUBLIC_PREVIEW_ACCESS_EMAILS=${NEXT_PUBLIC_PREVIEW_ACCESS_EMAILS}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - MINECRAFT_SERVICE_URL=${MINECRAFT_SERVICE_URL}
      - FILE_HOSTING_SERVICE_URL=${FILE_HOSTING_SERVICE_URL}
      - DATABASE_SERVICE_URL=${DATABASE_SERVICE_URL}
      - NODE_ENV=${NODE_ENV}
      - FLASK_ENV=${FLASK_ENV}
      - FLASK_DEBUG=${FLASK_DEBUG}
      - FLASK_PORT=${FLASK_PORT}
      - JWT_SECRET=${JWT_SECRET}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - MAIN_DOMAIN=${MAIN_DOMAIN}
      - PANEL_DOMAIN=${PANEL_DOMAIN}
      - NEXT_PUBLIC_MAIN_DOMAIN=${NEXT_PUBLIC_MAIN_DOMAIN}
      # Database connection
      - DATABASE_URL=postgresql://${POSTGRES_USER:-blocksconnect}:${POSTGRES_PASSWORD:-blocksconnect_password}@postgres:5432/blocksconnect

    depends_on:
      postgres:
        condition: service_healthy
      minecraft:
        condition: service_started
    networks:
      - blocksconnect-network
    restart: unless-stopped

  minecraft:
    build:
      context: ./services/minecraft
      dockerfile: Dockerfile
    ports:
      - "5001:5001"
    volumes:
      - ./shared/config:/app/config
      - ./services/minecraft:/app
      - //var/run/docker.sock:/var/run/docker.sock
      - minecraft_data:/data
    environment:
      - PYTHONPATH=/app
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FLASK_ENV=${FLASK_ENV}
      - FLASK_DEBUG=${FLASK_DEBUG}
      - JWT_SECRET=${JWT_SECRET}
      - MINECRAFT_SERVICE_URL=${MINECRAFT_SERVICE_URL}
      # Database connection
      - DATABASE_URL=postgresql://${POSTGRES_USER:-blocksconnect}:${POSTGRES_PASSWORD:-blocksconnect_password}@postgres:5432/blocksconnect
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - blocksconnect-network
    restart: unless-stopped

networks:
  blocksconnect-network:
    driver: bridge
  minecraft-servers-network:
    driver: bridge

volumes:
  file-storage:
    driver: local
  postgres_data:
    driver: local
  minecraft_data:
    driver: local

